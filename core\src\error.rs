/*!
# Error Types

Comprehensive error handling for the Wellbot Bridge Service.
*/

use thiserror::Error;

/// Main error type for the bridge service
#[derive(Error, Debug)]
pub enum BridgeError {
    #[error("HTTP client error: {0}")]
    Http(#[from] reqwest::Error),

    #[error("AI service error: {message}, status: {status:?}")]
    AiService {
        message: String,
        status: Option<reqwest::StatusCode>,
    },

    #[error("Authentication error: {0}")]
    Authentication(String),

    #[error("Rate limit exceeded: {0}")]
    RateLimit(String),

    #[error("Service unavailable: {0}")]
    ServiceUnavailable(String),

    #[error("Serialization error: {0}")]
    SerializationError(String),
}

/// Result type alias for bridge operations
pub type BridgeResult<T> = Result<T, BridgeError>;
