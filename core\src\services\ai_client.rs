/*!
# AI Service Client

HTTP client for communicating with the genuis FastAPI service.
*/
use std::time::Duration;

use crate::{
    config::AiServiceConfig,
    error::{BridgeError, BridgeResult},
    types::{ExplanationRequest, ExplanationResponse},
};
use reqwest::{Client, StatusCode};
use tracing::{debug, error, info, instrument, warn};
/// HTTP client for AI service communication
#[derive(Debug, Clone)]
pub struct AiClient {
    client: Client,
    config: AiServiceConfig,
}

impl AiClient {
    /// Create a new AI client with configuration
    pub fn new(config: AiServiceConfig) -> BridgeResult<Self> {
        let client = Client::builder()
            .timeout(Duration::from_secs(config.timeout_secs))
            .user_agent(format!("wellbot-bridge/{}", env!("CARGO_PKG_VERSION")))
            .build()?;

        info!(
            "AI client initialized - Base URL: {}, Timeout: {}s",
            config.base_url, config.timeout_secs
        );

        Ok(Self { client, config })
    }

    /// Generate explanation using AI service
    #[instrument(skip(self, request), fields(prompt_len = request.prompt.len()))]
    pub async fn generate_explanation(
        &self,
        request: ExplanationRequest,
    ) -> BridgeResult<ExplanationResponse> {
        let url = format!("{}/explanations", self.config.base_url);

        debug!("Sending AI request to: {}", url);

        // Add default values if not provided
        let request = ExplanationRequest {
            prompt: request.prompt,
            temperature: request.temperature.or(self.config.default_temperature),
            max_tokens: request.max_tokens.or(self.config.default_max_tokens),
        };

        let response = self
            .client
            .post(&url)
            .header("X-API-Key", &self.config.api_key)
            .header("Content-Type", "application/json")
            .json(&request)
            .send()
            .await
            .map_err(BridgeError::Http)?;

        let status = response.status();
        debug!("AI service response status: {}", status);

        match status {
            StatusCode::OK => {
                let explanation_response: ExplanationResponse =
                    response.json().await.map_err(BridgeError::Http)?;

                info!(
                    "AI explanation generated successfully - Model: {}, Tokens: {}, Processing time: {:.2}ms",
                    explanation_response.model,
                    explanation_response.tokens_used,
                    explanation_response.processing_time_ms
                );

                Ok(explanation_response)
            }
            StatusCode::UNAUTHORIZED => {
                error!("AI service authentication failed - check API key");
                Err(BridgeError::Authentication(
                    "Invalid API key for AI service".to_string(),
                ))
            }
            StatusCode::TOO_MANY_REQUESTS => {
                warn!("AI service rate limit exceeded");
                Err(BridgeError::RateLimit(
                    "AI service rate limit exceeded".to_string(),
                ))
            }
            StatusCode::SERVICE_UNAVAILABLE | StatusCode::BAD_GATEWAY => {
                warn!("AI service temporarily unavailable");
                Err(BridgeError::ServiceUnavailable(
                    "AI service temporarily unavailable".to_string(),
                ))
            }
            _ => {
                let error_text = response
                    .text()
                    .await
                    .unwrap_or_else(|_| "Unknown error".to_string());

                error!(
                    "AI service error - Status: {}, Body: {}",
                    status, error_text
                );

                Err(BridgeError::AiService {
                    message: error_text,
                    status: Some(status),
                })
            }
        }
    }

    /// Check if AI service is healthy
    #[instrument(skip(self))]
    pub async fn health_check(&self) -> BridgeResult<bool> {
        let url = format!("{}/health", self.config.base_url);

        debug!("Checking AI service health at: {}", url);

        match self
            .client
            .get(&url)
            .header("X-API-Key", &self.config.api_key)
            .timeout(Duration::from_secs(10)) // Shorter timeout for health checks
            .send()
            .await
        {
            Ok(response) => {
                let is_healthy = response.status().is_success();
                debug!("AI service health check result: {}", is_healthy);
                Ok(is_healthy)
            }
            Err(e) => {
                warn!("AI service health check failed: {}", e);
                Ok(false) // Don't propagate error, just return unhealthy status
            }
        }
    }

    /// Test connection to AI service
    #[instrument(skip(self))]
    pub async fn test_connection(&self) -> BridgeResult<()> {
        info!("Testing connection to AI service...");

        let test_request = ExplanationRequest {
            prompt: "Test connection".to_string(),
            temperature: Some(0.1),
            max_tokens: Some(10i32),
        };

        match self.generate_explanation(test_request).await {
            Ok(_) => {
                info!("AI service connection test successful");
                Ok(())
            }
            Err(e) => {
                error!("AI service connection test failed: {}", e);
                Err(e)
            }
        }
    }
}
