use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// AI service request (genuis API)
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ExplanationRequest {
    pub prompt: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub temperature: Option<f32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub max_tokens: Option<u32>,
}

/// AI service response (genuis API)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExplanationResponse {
    pub content: String,
    pub model: String,
    pub timestamp: DateTime<Utc>,
    pub tokens_used: u32,
    pub processing_time_ms: f64,
}
