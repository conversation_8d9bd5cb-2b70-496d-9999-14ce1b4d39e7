/*!
# 🤖 AI Client Interactive Demo

A beautiful interactive demonstration of the Wellbot AI Client service using cliclack.
This example showcases the AI client's capabilities with a modern, user-friendly CLI interface.

## Features
- ✨ Beautiful interactive prompts
- 🎨 Styled output with colors and emojis
- 🔧 Configuration management
- 🚀 Real-time AI interactions
- 📊 Performance metrics display
- 🔍 Health monitoring
- 🎯 Error handling demonstrations

## Usage
```bash
cargo run --example ai_client_demo --features examples
```
*/

use cliclack::{
    confirm, input, intro, log, multiselect, note, outro, outro_cancel, select, spinner,
};
use console::style;
use std::{thread, time::Duration};

use wellbot_bridge::{
    config::{AiServiceConfig, Config},
    services::ai_client::AiClient,
    types::{ExplanationRequest, ExplanationResponse},
};

#[tokio::main]
async fn main() -> std::io::Result<()> {
    // Set up Ctrl-C handler for graceful exit
    ctrlc::set_handler(move || {}).expect("setting Ctrl-C handler");

    // Clear screen and show beautiful intro
    cliclack::clear_screen()?;

    intro(style(" 🤖 Wellbot AI Client Demo ").on_cyan().black())?;

    log::info("Welcome to the Wellbot AI Client interactive demonstration!")?;
    log::remark("This demo showcases the AI service integration capabilities")?;

    // Configuration setup
    let config_choice = select("How would you like to configure the AI service?")
        .initial_value("default")
        .item(
            "default",
            "Use default configuration",
            "Recommended for testing",
        )
        .item("custom", "Custom configuration", "Advanced users")
        .item("env", "Load from environment", "Production-like setup")
        .interact()?;

    let config = match config_choice {
        "custom" => setup_custom_config().await?,
        "env" => {
            log::step("Loading configuration from environment variables...")?;
            match Config::from_env() {
                Ok(config) => {
                    log::success("Configuration loaded successfully!")?;
                    config
                }
                Err(e) => {
                    log::warning(format!("Failed to load from env: {}", e))?;
                    log::remark("Falling back to default configuration")?;
                    get_default_config()
                }
            }
        }
        _ => {
            log::step("Using default configuration")?;
            get_default_config()
        }
    };

    // Display configuration summary
    display_config_summary(&config)?;

    // Initialize AI client
    let spinner = spinner();
    spinner.start("🔧 Initializing AI client...");
    thread::sleep(Duration::from_millis(800)); // Simulate initialization

    let ai_client = match AiClient::new(config.ai_service.clone()) {
        Ok(client) => {
            spinner.stop("✅ AI client initialized successfully!");
            client
        }
        Err(e) => {
            spinner.stop("❌ Failed to initialize AI client");
            log::error(format!("Initialization error: {}", e))?;
            outro_cancel("Demo terminated due to initialization failure")?;
            return Ok(());
        }
    };

    // Health check
    if confirm("🏥 Would you like to perform a health check?").interact()? {
        perform_health_check(&ai_client).await?;
    }

    // Main demo loop
    loop {
        let action = select("What would you like to do?")
            .initial_value("explain")
            .item(
                "explain",
                "🧠 Generate AI Explanation",
                "Ask the AI to explain something",
            )
            .item(
                "test",
                "🔬 Test Connection",
                "Verify AI service connectivity",
            )
            .item(
                "batch",
                "📦 Batch Processing Demo",
                "Process multiple requests",
            )
            .item(
                "config",
                "⚙️  View Configuration",
                "Display current settings",
            )
            .item("health", "🏥 Health Check", "Check service status")
            .item("exit", "🚪 Exit Demo", "End the demonstration")
            .interact()?;

        match action {
            "explain" => generate_explanation_demo(&ai_client).await?,
            "test" => test_connection_demo(&ai_client).await?,
            "batch" => batch_processing_demo(&ai_client).await?,
            "config" => display_config_summary(&config)?,
            "health" => perform_health_check(&ai_client).await?,
            "exit" => break,
            _ => log::warning("Unknown action selected")?,
        }

        if !confirm("🔄 Continue with another action?").interact()? {
            break;
        }
    }

    outro(format!(
        "🎉 Thanks for trying the Wellbot AI Client Demo!\n\n{}",
        style("For more information, visit: https://github.com/wellbot/wellbot")
            .cyan()
            .underlined()
    ))?;

    Ok(())
}

/// Setup custom configuration interactively
async fn setup_custom_config() -> std::io::Result<Config> {
    log::step("Setting up custom AI service configuration")?;

    let base_url: String = input("🌐 AI Service Base URL")
        .placeholder("http://localhost:8000/api/v1")
        .validate(|input: &String| {
            if input.is_empty() {
                Err("Please enter a base URL")
            } else if !input.starts_with("http") {
                Err("URL must start with http:// or https://")
            } else {
                Ok(())
            }
        })
        .interact()?;

    let api_key: String = input("🔑 API Key")
        .placeholder("wellbot-dev-key-2025")
        .validate(|input: &String| {
            if input.is_empty() {
                Err("Please enter an API key")
            } else {
                Ok(())
            }
        })
        .interact()?;

    let timeout_secs: u64 = input("⏱️  Timeout (seconds)")
        .placeholder("60")
        .interact()?;

    let advanced_options = confirm("🔧 Configure advanced options?").interact()?;

    let (retry_attempts, retry_delay_secs, default_temperature, default_max_tokens) =
        if advanced_options {
            let retry_attempts: u32 = input("🔄 Retry attempts").placeholder("3").interact()?;

            let retry_delay_secs: u64 = input("⏳ Retry delay (seconds)")
                .placeholder("2")
                .interact()?;

            let temp_input: String = input("🌡️  Default temperature (0.0-1.0, or leave empty)")
                .placeholder("0.7")
                .interact()?;

            let default_temperature = if temp_input.is_empty() {
                None
            } else {
                Some(temp_input.parse().unwrap_or(0.7))
            };

            let tokens_input: String = input("🎯 Default max tokens (or leave empty)")
                .placeholder("4000")
                .interact()?;

            let default_max_tokens = if tokens_input.is_empty() {
                None
            } else {
                Some(tokens_input.parse().unwrap_or(4000))
            };

            (
                retry_attempts,
                retry_delay_secs,
                default_temperature,
                default_max_tokens,
            )
        } else {
            (3, 2, Some(0.7), Some(4000))
        };

    log::success("✅ Custom configuration created!")?;

    Ok(Config {
        ai_service: AiServiceConfig {
            base_url,
            api_key,
            timeout_secs,
            retry_attempts,
            retry_delay_secs,
            default_temperature,
            default_max_tokens,
        },
    })
}

/// Get default configuration
fn get_default_config() -> Config {
    Config {
        ai_service: AiServiceConfig {
            base_url: "http://localhost:8000/api/v1".to_string(),
            api_key: "wellbot-dev-key-2025".to_string(),
            timeout_secs: 60,
            retry_attempts: 3,
            retry_delay_secs: 2,
            default_temperature: Some(0.7),
            default_max_tokens: Some(4000),
        },
    }
}

/// Display configuration summary
fn display_config_summary(config: &Config) -> std::io::Result<()> {
    let config_info = format!(
        "🌐 Base URL: {}\n🔑 API Key: {}***\n⏱️  Timeout: {}s\n🔄 Retry Attempts: {}\n⏳ Retry Delay: {}s\n🌡️  Temperature: {}\n🎯 Max Tokens: {}",
        style(&config.ai_service.base_url).cyan(),
        style(
            &config
                .ai_service
                .api_key
                .chars()
                .take(8)
                .collect::<String>()
        )
        .yellow(),
        style(config.ai_service.timeout_secs).green(),
        style(config.ai_service.retry_attempts).green(),
        style(config.ai_service.retry_delay_secs).green(),
        style(
            config
                .ai_service
                .default_temperature
                .map_or("None".to_string(), |t| t.to_string())
        )
        .magenta(),
        style(
            config
                .ai_service
                .default_max_tokens
                .map_or("None".to_string(), |t| t.to_string())
        )
        .magenta()
    );

    note("⚙️ Current Configuration", config_info)?;
    Ok(())
}

/// Perform health check demonstration
async fn perform_health_check(ai_client: &AiClient) -> std::io::Result<()> {
    let spinner = spinner();
    spinner.start("🏥 Checking AI service health...");

    match ai_client.health_check().await {
        Ok(is_healthy) => {
            if is_healthy {
                spinner.stop("✅ AI service is healthy!");
                log::success("All systems operational")?;
            } else {
                spinner.stop("⚠️ AI service health check failed");
                log::warning("Service may be experiencing issues")?;
            }
        }
        Err(e) => {
            spinner.stop("❌ Health check error");
            log::error(format!("Health check failed: {}", e))?;
        }
    }

    Ok(())
}

/// Generate explanation demonstration
async fn generate_explanation_demo(ai_client: &AiClient) -> std::io::Result<()> {
    log::step("🧠 AI Explanation Generator")?;

    let prompt_options = multiselect("Select prompt type(s) or choose custom:")
        .initial_values(vec!["custom"])
        .item(
            "quantum",
            "Quantum Computing",
            "Explain quantum computing basics",
        )
        .item(
            "blockchain",
            "Blockchain Technology",
            "Explain how blockchain works",
        )
        .item(
            "ai",
            "Artificial Intelligence",
            "Explain AI and machine learning",
        )
        .item("rust", "Rust Programming", "Explain Rust language features")
        .item("custom", "Custom Prompt", "Enter your own prompt")
        .interact()?;

    let prompts = if prompt_options.contains(&"custom") {
        let custom_prompt: String = input("✍️ Enter your custom prompt")
            .placeholder("Explain something interesting...")
            .validate(|input: &String| {
                if input.trim().is_empty() {
                    Err("Please enter a non-empty prompt")
                } else if input.len() > 1000 {
                    Err("Prompt too long (max 1000 characters)")
                } else {
                    Ok(())
                }
            })
            .interact()?;
        vec![custom_prompt]
    } else {
        prompt_options.iter().map(|option| {
            match *option {
                "quantum" => "Explain quantum computing in simple terms, including its key principles and potential applications.".to_string(),
                "blockchain" => "Explain blockchain technology, how it works, and why it's considered secure and decentralized.".to_string(),
                "ai" => "Explain artificial intelligence and machine learning, including how neural networks learn from data.".to_string(),
                "rust" => "Explain the Rust programming language, its memory safety features, and why it's popular for systems programming.".to_string(),
                _ => option.to_string()
            }
        }).collect()
    };

    // Advanced options
    let use_advanced = confirm("🔧 Configure advanced parameters?").interact()?;
    let (temperature, max_tokens) = if use_advanced {
        let temp_str: String = input("🌡️ Temperature (0.0-1.0)")
            .placeholder("0.7")
            .validate(|input: &String| match input.parse::<f32>() {
                Ok(val) if (0.0..=1.0).contains(&val) => Ok(()),
                Ok(_) => Err("Temperature must be between 0.0 and 1.0"),
                Err(_) => Err("Please enter a valid number"),
            })
            .interact()?;
        let temp = temp_str.parse::<f32>().unwrap();

        let tokens_str: String = input("🎯 Max tokens")
            .placeholder("4000")
            .validate(|input: &String| match input.parse::<u32>() {
                Ok(val) if val > 0 && val <= 8000 => Ok(()),
                Ok(_) => Err("Max tokens must be between 1 and 8000"),
                Err(_) => Err("Please enter a valid number"),
            })
            .interact()?;
        let tokens = tokens_str.parse::<u32>().unwrap();

        (Some(temp), Some(tokens))
    } else {
        (None, None)
    };

    // Process each prompt
    for (i, prompt) in prompts.iter().enumerate() {
        if prompts.len() > 1 {
            log::step(format!("Processing prompt {} of {}", i + 1, prompts.len()))?;
        }

        let request = ExplanationRequest {
            prompt: prompt.clone(),
            temperature,
            max_tokens,
        };

        let spinner = spinner();
        spinner.start("🤖 Generating AI explanation...");

        let start_time = std::time::Instant::now();

        match ai_client.generate_explanation(request).await {
            Ok(response) => {
                let elapsed = start_time.elapsed();
                spinner.stop("✅ Explanation generated successfully!");

                display_explanation_result(&response, elapsed)?;
            }
            Err(e) => {
                spinner.stop("❌ Failed to generate explanation");
                log::error(format!("Generation error: {}", e))?;

                // Show error details based on error type
                match e.to_string().as_str() {
                    s if s.contains("Authentication") => {
                        log::warning("💡 Tip: Check your API key configuration")?;
                    }
                    s if s.contains("RateLimit") => {
                        log::warning("💡 Tip: Wait a moment before trying again")?;
                    }
                    s if s.contains("ServiceUnavailable") => {
                        log::warning("💡 Tip: The AI service might be temporarily down")?;
                    }
                    _ => {
                        log::warning(
                            "💡 Tip: Check your network connection and service configuration",
                        )?;
                    }
                }
            }
        }

        if i < prompts.len() - 1 {
            thread::sleep(Duration::from_millis(500)); // Brief pause between requests
        }
    }

    Ok(())
}

/// Display explanation result with beautiful formatting
fn display_explanation_result(
    response: &ExplanationResponse,
    elapsed: Duration,
) -> std::io::Result<()> {
    // Format the content with proper wrapping
    let content = if response.content.len() > 500 {
        format!(
            "{}...\n\n{}",
            &response.content[..500],
            style("(Content truncated for display)").dim()
        )
    } else {
        response.content.clone()
    };

    let result_info = format!(
        "📝 Content:\n{}\n\n📊 Metadata:\n🤖 Model: {}\n🎯 Tokens Used: {}\n⚡ Processing Time: {:.2}ms\n🕒 Response Time: {:.2}ms\n📅 Timestamp: {}",
        style(&content).white(),
        style(&response.model).cyan(),
        style(response.tokens_used).yellow(),
        style(response.processing_time_ms).green(),
        style(elapsed.as_millis()).green(),
        style(response.timestamp.format("%Y-%m-%d %H:%M:%S UTC")).blue()
    );

    note("🎉 AI Explanation Result", result_info)?;
    Ok(())
}

/// Test connection demonstration
async fn test_connection_demo(ai_client: &AiClient) -> std::io::Result<()> {
    log::step("🔬 Testing AI service connection")?;

    let spinner = spinner();
    spinner.start("🔗 Testing connection to AI service...");

    match ai_client.test_connection().await {
        Ok(()) => {
            spinner.stop("✅ Connection test successful!");
            log::success("AI service is reachable and responding correctly")?;
        }
        Err(e) => {
            spinner.stop("❌ Connection test failed");
            log::error(format!("Connection error: {}", e))?;

            // Provide helpful troubleshooting tips
            note(
                "🔧 Troubleshooting Tips",
                "• Check if the AI service is running\n• Verify the base URL is correct\n• Ensure the API key is valid\n• Check network connectivity\n• Review firewall settings",
            )?;
        }
    }

    Ok(())
}

/// Batch processing demonstration
async fn batch_processing_demo(ai_client: &AiClient) -> std::io::Result<()> {
    log::step("📦 Batch Processing Demo")?;

    let batch_prompts = [
        "Explain photosynthesis in plants",
        "What is the theory of relativity?",
        "How do computers process information?",
        "Describe the water cycle",
    ];

    let process_all = confirm(format!(
        "Process {} sample prompts in batch?",
        batch_prompts.len()
    ))
    .interact()?;

    if !process_all {
        log::remark("Batch processing cancelled")?;
        return Ok(());
    }

    log::info(format!(
        "🚀 Starting batch processing of {} requests",
        batch_prompts.len()
    ))?;

    let mut successful = 0;
    let mut failed = 0;
    let start_time = std::time::Instant::now();

    for (i, prompt) in batch_prompts.iter().enumerate() {
        let spinner = spinner();
        spinner.start(format!(
            "Processing request {}/{}: {}",
            i + 1,
            batch_prompts.len(),
            if prompt.len() > 30 {
                format!("{}...", &prompt[..30])
            } else {
                prompt.to_string()
            }
        ));

        let request = ExplanationRequest {
            prompt: prompt.to_string(),
            temperature: Some(0.5),
            max_tokens: Some(200), // Shorter responses for batch demo
        };

        match ai_client.generate_explanation(request).await {
            Ok(response) => {
                spinner.stop(format!(
                    "✅ Request {}/{} completed",
                    i + 1,
                    batch_prompts.len()
                ));
                successful += 1;

                // Show brief result
                log::success(format!(
                    "Generated {} tokens in {:.1}ms",
                    response.tokens_used, response.processing_time_ms
                ))?;
            }
            Err(e) => {
                spinner.stop(format!(
                    "❌ Request {}/{} failed",
                    i + 1,
                    batch_prompts.len()
                ));
                failed += 1;
                log::warning(format!("Error: {}", e))?;
            }
        }

        // Brief pause between requests to be respectful to the API
        if i < batch_prompts.len() - 1 {
            thread::sleep(Duration::from_millis(1000));
        }
    }

    let total_time = start_time.elapsed();

    // Display batch processing summary
    let summary = format!(
        "📊 Batch Processing Summary:\n✅ Successful: {}\n❌ Failed: {}\n⏱️ Total Time: {:.2}s\n📈 Average Time per Request: {:.2}s",
        style(successful).green(),
        style(failed).red(),
        style(total_time.as_secs_f64()).blue(),
        style(total_time.as_secs_f64() / batch_prompts.len() as f64).blue()
    );

    note("🎯 Batch Processing Complete", summary)?;

    Ok(())
}
