/*!
# Configuration Module

Contains all configuration related code for the bridge.
*/

use anyhow::Result;
use serde::{Deserialize, Serialize};
use tracing::{info, warn};

/// AI service (genuis) configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AiServiceConfig {
    /// Base URL for AI service
    pub base_url: String,
    /// API key for authentication
    pub api_key: String,
    /// Request timeout in seconds
    pub timeout_secs: u64,
    /// Retry attempts for failed requests
    pub retry_attempts: u32,
    /// Retry delay in seconds
    pub retry_delay_secs: u64,
    /// Default temperature for AI requests
    pub default_temperature: Option<f32>,
    /// Default max tokens for AI requests
    pub default_max_tokens: Option<i32>,
}

/// Main configuration structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    /// AI service (genuis) configuration
    pub ai_service: AiServiceConfig,
}

impl Config {
    /// Validate configuration
    pub fn validate(&self) -> Result<()> {
        Ok(())
    }

    /// Load configuration from environment variables
    pub fn from_env() -> Result<Self> {
        // Load .env file if it exists
        if let Err(e) = dotenvy::dotenv() {
            warn!("No .env file found or error loading it: {}", e);
        }

        let config = Self {
            ai_service: AiServiceConfig {
                base_url: get_env("AI_SERVICE_URL", "http://localhost:8000/api/v1".into()),
                api_key: get_env("AI_SERVICE_API_KEY", "wellbot-dev-key-2025".into()),
                timeout_secs: get_env("AI_SERVICE_TIMEOUT", 60),
                retry_attempts: get_env("AI_SERVICE_RETRY_ATTEMPTS", 3),
                retry_delay_secs: get_env("AI_SERVICE_RETRY_DELAY", 2),
                default_temperature: get_env_opt("AI_SERVICE_DEFAULT_TEMPERATURE"),
                default_max_tokens: get_env_opt("AI_SERVICE_DEFAULT_MAX_TOKENS"),
            },
        };

        config.validate()?;
        info!("Configuration loaded and validated successfully");

        Ok(config)
    }
}

// Helper functions for environment variable parsing
fn get_env<T>(key: &str, default: T) -> T
where
    T: std::str::FromStr + Clone,
{
    std::env::var(key)
        .ok()
        .and_then(|v| v.parse().ok())
        .unwrap_or(default)
}

fn get_env_opt<T>(key: &str) -> Option<T>
where
    T: std::str::FromStr,
{
    std::env::var(key).ok().and_then(|v| v.parse().ok())
}
