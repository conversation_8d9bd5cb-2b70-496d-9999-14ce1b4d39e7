use anyhow::Result;
use tracing::info;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    init_logging().await?;

    info!(
        "🚀 Starting Wellbot Bridge Service v{}",
        env!("CARGO_PKG_VERSION")
    );

    Ok(())
}

/// Initialize structured logging based on configuration
async fn init_logging() -> Result<()> {
    // Try to load config for logging settings, but use defaults if it fails
    let log_level = std::env::var("LOG_LEVEL").unwrap_or_else(|_| "info".to_string());
    let log_format = std::env::var("LOG_FORMAT").unwrap_or_else(|_| "pretty".to_string());

    let env_filter = format!("wellbot_bridge={},tower_http=debug", log_level);

    let subscriber = tracing_subscriber::registry().with(
        tracing_subscriber::EnvFilter::try_from_default_env().unwrap_or_else(|_| env_filter.into()),
    );

    match log_format.as_str() {
        "json" => {
            subscriber
                .with(tracing_subscriber::fmt::layer().json().with_target(false))
                .init();
        }
        _ => {
            subscriber
                .with(tracing_subscriber::fmt::layer().with_target(false))
                .init();
        }
    }

    info!(
        "📝 Logging initialized - Level: {}, Format: {}",
        log_level, log_format
    );
    Ok(())
}
